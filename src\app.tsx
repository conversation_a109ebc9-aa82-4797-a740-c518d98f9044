import React, { useEffect } from 'react';
import { useDidShow, useDidHide } from '@tarojs/taro';
import { Provider } from 'mobx-react';
import counterStore from './store/counter';
import BluetoothStore from './store/bluetooth';
import tarbarStore from './store/tarbar';
import bluetoothLink from './store/bluetoothLink';
import bluetoothService from './services/bluetoothService';
const store = {
  counterStore,
  BluetoothStore,
  tarbarStore,
  bluetoothLink,
};

// 全局样式
import './app.scss';
const App = props => {
  // 可以使用所有的 React Hooks
  useEffect(() => {});

  // 对应 onShow
  useDidShow(() => {
    // 小程序进入前台时重新连接蓝牙
    bluetoothService.reconnectBluetooth();
  });

  // 对应 onHide
  useDidHide(() => {
    // 小程序进入后台时断开蓝牙连接
    if (bluetoothService.isConnected) {
      console.log('小程序进入后台，断开蓝牙连接');
      bluetoothService.disconnect();
    }
  });

  return <Provider store={store}>{props.children}</Provider>;
};

export default App;

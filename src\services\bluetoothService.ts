import Taro from '@tarojs/taro';
import { observable, action } from 'mobx';
import bluetoothStore from '../store/bluetooth';
import bluetoothLinkStore from '../store/bluetoothLink';
import counterStore from '../store/counter';
import { getParseNotifyRecord, getStatus } from '@/config/notify';
import refreshStore from '../store/refreshStore';
import { obsidian } from 'react-syntax-highlighter/dist/cjs/styles/hljs';

/**
 * 蓝牙服务类，提供完整的蓝牙设备操作流程：
 * 1. 初始化蓝牙适配器
 * 2. 搜索蓝牙设备
 * 3. 连接目标设备
 * 4. 获取设备服务
 * 5. 获取特征值
 * 6. 启用通知监听数据变化
 * 7. 读写数据
 * 8. 断开连接
 *
 * 使用流程示例：
 * 1. initBluetoothAdapter() - 初始化适配器
 * 2. startDiscovery() - 开始搜索设备
 * 3. connectDevice(device) - 连接选中的设备
 * 4. 自动完成后续服务发现和特征值获取
 * 5. 使用writeBLECharacteristicValue发送数据
 * 6. 通过onBLECharacteristicValueChange接收数据
 * 7. disconnect() - 断开连接
 */
class BluetoothService {
  @observable deviceId = '';
  @observable characteristicId = '';
  @observable serviceId = '';
  @observable writeDefaultId = '';
  @observable readId = '';
  @observable localName = '未知';
  @observable isConnected = false;
  @observable devices: any[] = [];
  @observable filteredDevices: any[] = []; // 新增用于存储筛选后的设备列表
  @observable isLoading = false;
  @observable connectionListenerSet = false; // 跟踪连接状态监听器是否已设置

  /**
   * 初始化蓝牙适配器 - 第一步
   * 1. 关闭已有适配器
   * 2. 打开新适配器
   * 3. 检查适配器状态
   * @returns {Promise<boolean>} 初始化是否成功
   * @throws 初始化失败时抛出错误
   */
  @action
  async initBluetoothAdapter() {
    try {
      this.isLoading = true;

      // 1. 检查蓝牙权限
      const bluetoothAuth = await this.checkBluetoothPermission();
      if (!bluetoothAuth) {
        throw new Error('蓝牙权限未授权');
      }

      // 2. 安卓系统需要检查定位权限
      const systemInfo = await Taro.getSystemInfo();
      if (systemInfo.platform.toLowerCase() === 'android') {
        let locationEnabled = systemInfo.locationEnabled;
        let locationAuthorized = systemInfo.locationAuthorized;
        if (locationEnabled == false || locationAuthorized == false) {
          Taro.showModal({
            title: '提示',
            content: '请打开定位服务功能,部分手机可能会导致搜索不到设备!',
            showCancel: false,
          });
        }
        const locationAuth = await this.checkLocationPermission();
        if (!locationAuth) {
          throw new Error('定位权限未授权');
        }
      }
      console.log('当前？');
      await Taro.closeBluetoothAdapter({
        success: () => console.log('蓝牙适配器已关闭'),
        fail: err => console.error('关闭蓝牙适配器失败:', err),
      });

      const res = await Taro.openBluetoothAdapter({
        success: () => console.log('蓝牙适配器已打开'),
        fail: err => {
          // this.handleBluetoothError(err)
          throw err;
        },
      });
      const adapterState = await Taro.getBluetoothAdapterState({
        success: res => {
          console.log('获取蓝牙适配器状态成功', res);
        },
        fail: err => console.error('获取蓝牙适配器状态失败:', err),
      });

      if (!adapterState.available) {
        throw new Error('蓝牙适配器不可用');
      }

      return true;
    } catch (error) {
      console.error('初始化蓝牙失败:', error);
      return false;
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * 开始搜索设备 - 第二步
   * 1. 启动蓝牙设备发现
   * 2. 设置设备发现监听器
   * 注意：搜索到的设备会存储在this.devices中
   * @throws 搜索失败时抛出错误
   */
  @action
  async startDiscovery() {
    try {
      await Taro.startBluetoothDevicesDiscovery({
        allowDuplicatesKey: false,
        interval: 0,
        success: () => console.log('开始搜索蓝牙设备'),
        fail: err => console.error('搜索蓝牙设备失败:', err),
      });
      this.setupDeviceFoundListener();
    } catch (error) {
      console.error('搜索设备失败:', error);
      throw error;
    }
  }

  /**
   * 停止搜索设备
   * 通常在连接设备后调用
   */
  @action
  async stopDiscovery() {
    try {
      await Taro.stopBluetoothDevicesDiscovery({
        success: () => console.log('停止搜索蓝牙设备'),
        fail: err => console.error('停止搜索蓝牙设备失败:', err),
      });
      this.isLoading = false;
    } catch (error) {
      console.error('停止搜索失败:', error);
    }
  }

  /**
   * 连接设备 - 第三步
   * 1. 创建BLE连接
   * 2. 获取设备服务
   * 3. 获取特征值
   * 4. 设置连接状态监听
   * @param {Object} device 要连接的设备对象
   * @returns {Promise<boolean>} 连接是否成功
   * @throws 连接失败时抛出错误
   */
  @action
  async connectDevice(device: any) {
    console.log(device, '设备连接？');
    try {
      Taro.showLoading({ title: '蓝牙连接中...' });
      await Taro.createBLEConnection({
        deviceId: device.deviceId,
      });
      // hideLoading
      this.deviceId = device.deviceId;
      this.localName = device.localName || device.name;

      const services = await this.getDeviceServices(device.deviceId);
      await this.getDeviceCharacteristics(device.deviceId, services[0].uuid);

    

      if (!bluetoothLinkStore.state.connectionListenerSet) {
        this.setupConnectionStateListener();
        bluetoothLinkStore.setState({ connectionListenerSet: true });
      }
      this.isConnected = true;
      bluetoothLinkStore.setState({ bluetoothLink: true });
      return true;
    } catch (error) {
      console.error('连接设备失败:', error);
      throw error;
    } finally {
      Taro.hideLoading();
    }
  }

  /**
   * 断开当前设备连接 - 最后一步
   * 1. 关闭BLE连接
   * 2. 关闭蓝牙适配器
   * 3. 重置所有状态
   */
  @action
  async disconnect() {
    try {
      await Taro.closeBLEConnection({ deviceId: this.deviceId });
      await Taro.closeBluetoothAdapter({
        success: () => console.log('蓝牙适配器已关闭'),
        fail: err => console.error('关闭蓝牙适配器失败:', err),
      });
      this.resetState();
      bluetoothLinkStore.setState({ bluetoothLink: false });
      Taro.removeStorageSync('BluetoothStore');
      refreshStore.isOpenWebSocket(false);
    } catch (error) {
      console.error('断开连接失败:', error);
    }
  }

  /**
   * 重新连接蓝牙 - 小程序前台时调用
   * 检查必要条件后自动重连蓝牙设备
   */
  @action
  async reconnectBluetooth() {
    try {
      // 检查是否禁用重连
      if (Taro.getStorageSync('noBlueToothAgain')) {
        Taro.removeStorageSync('noBlueToothAgain');
        return;
      }

      // 检查必要的用户信息
      const phone = Taro.getStorageSync('phone');
      const seqNo = Taro.getStorageSync('seqNo');

      if (!phone || !seqNo) {
        console.log('缺少用户信息，跳过蓝牙重连');
        return;
      }

      // 如果已经连接，则不需要重连
      if (this.isConnected) {
        console.log('蓝牙已连接，无需重连');
        return;
      }

      console.log('小程序进入前台，开始重连蓝牙');

      // 初始化蓝牙适配器并开始搜索设备
      const initResult = await this.initBluetoothAdapter();
      if (initResult) {
        await this.startDiscovery();
        console.log('蓝牙重连初始化成功，开始搜索设备');
      }
    } catch (error) {
      console.error('蓝牙重连失败:', error);
    }
  }

  /**
   * 获取设备服务 - 内部方法，由connectDevice调用
   * @param {string} deviceId 设备ID
   * @returns {Promise} 服务列表
   */
  private async getDeviceServices(deviceId: string) {
    const res = await Taro.getBLEDeviceServices({ deviceId });
    return res.services;
  }

  /**
   * 获取设备特征值 - 内部方法，由connectDevice调用
   * 1. 查找可写特征值
   * 2. 查找可读特征值
   * 3. 启用通知
   * @param {string} deviceId 设备ID
   * @param {string} serviceId 服务ID
   */
  private async getDeviceCharacteristics(deviceId: string, serviceId: string) {
    const res = await Taro.getBLEDeviceCharacteristics({
      deviceId,
      serviceId,
    });

    const writeChar = res.characteristics.find(c => c.properties.write);
    const readChar = res.characteristics.find(c => c.properties.read);

    if (writeChar && readChar) {
      this.serviceId = serviceId;
      this.writeDefaultId = writeChar.uuid;
      this.readId = readChar.uuid;
      await this.enableNotifications();
    }
    return res.characteristics;
  }

  /**
   * 启用特征值变化通知 - 内部方法
   * 启用后会通过onBLECharacteristicValueChange接收数据
   */
  private async enableNotifications() {
    console.log(
      this.characteristicId,
      this.deviceId,
      this.localName,
      this.readId,
      this.serviceId,
      this.writeDefaultId,
      '这些是吗？'
    );
    //这里到首页调用store.counterStore
    //不调用其他的方法
    const counterStore1 = {
      characteristicId: this.characteristicId,
      deviceId: this.deviceId,
      localName: this.localName,
      readId: this.readId,
      serviceId: this.serviceId,
      writeDefaultId: this.writeDefaultId,
    };
    counterStore.setState(counterStore1);
  }

  /**
   * 设置设备发现监听器 - 内部方法
   * 过滤并存储名称包含'BAT'或'BLE-'的设备
   */
  private setupDeviceFoundListener() {
    // let count = 0;
    Taro.onBluetoothDeviceFound((res: any) => {
      const devices = res.devices;
      if (devices.length >= 1) {
        const items = devices[0];
        const name = items.localName || items.name;
        const blueSignalIsOk = items.RSSI != 0 && items.RSSI > -90; //给定蓝牙判断值
        const isStorageEqu = name == Taro.getStorageSync('seqNo'); //是否是缓存设备数据
        const isPreviousEqu = items.deviceId == counterStore.state.deviceId; //是否上个蓝牙连接设备
        const BlueReconnectionStatus =
          bluetoothLinkStore.state.BlueReconnectionStatus; //蓝牙重连状态
        const isAutoBlue = Taro.getStorageSync('autoBluetooth'); //手动或自动
        if (name.includes('BAT') || (name.includes('BLE-') && blueSignalIsOk)) {
          //手动连接
          if (isAutoBlue == false) {
            this.getBluetoothDevices();
          }
          //自动连接
          if (isStorageEqu && isAutoBlue) {
            this.connectDevice(items);
          }
          //自动重连
          if (isPreviousEqu && BlueReconnectionStatus) {
            this.connectDevice(items);
          }
        }
      }
    });
  }

  /**
   * 筛选设备列表
   */
  private async getBluetoothDevices() {
    const DevicesList = await Taro.getBluetoothDevices();
    if (DevicesList.devices.length > 0) {
      let devicesListArr: any = DevicesList.devices.filter(item => {
        const name = item.localName || item.name;
        return (
          (name.includes('BAT') || name.includes('BLE-')) &&
          item.RSSI != 0 &&
          item.RSSI > -80
        );
      });
      this.filteredDevices = devicesListArr;
    } else {
      Taro.showModal({
        title: '温馨提示',
        content: '无法搜索到蓝牙设备，请重试',
        showCancel: false,
      });
    }
  }
  /**
   * 获取筛选后的设备列表 - 供外部调用
   * @returns {any[]} 筛选后的设备列表
   */
  @action
  getFilteredDevices(): any[] {
    return this.filteredDevices;
  }

  /**
   * 设置连接状态监听器 - 内部方法
   * 连接断开时自动处理
   */
  private connectionStateCallback: ((res: any) => void) | null = null;

  private setupConnectionStateListener() {
    this.connectionStateCallback = res => {
      console.log('蓝牙断开参数', res);
      if (!res.connected) {
        bluetoothStore.setState({
          电池编号: '--',
          电流: '--',
          剩余电量: '--',
          累计循环放电次数: '--',
          预计使用时间: '',
          软件版本: '--',
          电池状态: '离线',
          硬件状态: '--',
          温度: [],
          电池电压: [],
          校验: '',
          告警: '',
        });
        Taro.setStorageSync('toFour', true);
        refreshStore.blueToFour();
        this.checkBluetooth(res.deviceId);
        this.handleDisconnected();
      }
    };
    Taro.onBLEConnectionStateChange(this.connectionStateCallback);
  }

  /**
   * 蓝牙重连
   * @param {deviceId} any 根据deviceId重连
   */
  private async checkBluetooth(deviceId: any) {
    const state = await Taro.getBluetoothAdapterState();
    if (state.available == false) {
      //主动关闭蓝牙 断开连接
      //开启蓝牙重连状态 计数重连次数
      bluetoothLinkStore.setState({ BlueReconnectionStatus: true });
      const TimerID1 = setInterval(() => {
        bluetoothLinkStore.setState({
          ReconnectionCount: bluetoothLinkStore.state.ReconnectionCount + 1,
        });
        if (bluetoothLinkStore.state.ReconnectionCount > 10) {
          clearInterval(TimerID1);
          bluetoothLinkStore.setState({ ReconnectionCount: 0 });
          return;
        }
        if (bluetoothLinkStore.state.bluetoothLink) {
          clearInterval(TimerID1);
          bluetoothLinkStore.setState({ ReconnectionCount: 0 });
          return;
        }
        const initResult = this.initBluetoothAdapter();
        initResult.then((res: boolean) => {
          console.log(res);
          this.startDiscovery();
        });
      }, 8000);
    } else {
      //未主动关闭蓝牙 断开连接，以及异常
      //开启蓝牙重连状态
      bluetoothLinkStore.setState({ BlueReconnectionStatus: true });
      const TimerID2 = setInterval(() => {
        bluetoothLinkStore.setState({
          ReconnectionCount: bluetoothLinkStore.state.ReconnectionCount + 1,
        });
        if (bluetoothLinkStore.state.ReconnectionCount > 10) {
          clearInterval(TimerID2);
          bluetoothLinkStore.setState({ ReconnectionCount: 0 });
          return;
        }
        if (bluetoothLinkStore.state.bluetoothLink) {
          clearInterval(TimerID2);
          bluetoothLinkStore.setState({ BlueReconnectionStatus: false });
          bluetoothLinkStore.setState({ ReconnectionCount: 0 });
          return;
        }
        const initResult = this.initBluetoothAdapter();
        initResult.then((res: boolean) => {
          console.log(res);
          if (res) {
            this.startDiscovery();
          }
        });
      }, 8000);
    }
  }

  /**
   * 关闭连接状态监听器
   */
  @action
  closeConnectionStateListener() {
    try {
      if (this.connectionStateCallback) {
        Taro.offBLEConnectionStateChange(this.connectionStateCallback);
        this.connectionStateCallback = null;
      }
      this.connectionListenerSet = false;
      console.log('已关闭蓝牙连接状态监听器');
    } catch (error) {
      console.error('关闭蓝牙连接状态监听器失败:', error);
    }
  }

  /**
   * 处理断开连接 - 内部方法
   * 1. 显示断开提示
   * 2. 重置状态
   * 3. 5秒后尝试重新初始化
   */
  private handleDisconnected() {
    Taro.showToast({ title: '蓝牙已断开' });
    this.resetState();
    this.isConnected = false;
    bluetoothLinkStore.setState({ bluetoothLink: false });
    Taro.removeStorageSync('BluetoothStore');
    refreshStore.isOpenWebSocket(false);
  }

  /**
   * 重置所有状态 - 内部方法
   */
  @action
  private resetState() {
    this.deviceId = '';
    this.serviceId = '';
    this.writeDefaultId = '';
    this.readId = '';
    this.localName = '未知';
    this.isConnected = false;
    this.devices = [];
    this.connectionListenerSet = false;
  }

  /**
   * ArrayBuffer转16进制字符串 - 工具方法
   * @param {ArrayBuffer} buffer 要转换的buffer
   * @returns {string} 16进制字符串
   */
  private ab2hex(buffer: ArrayBuffer) {
    const hexArr = Array.prototype.map.call(new Uint8Array(buffer), bit =>
      ('00' + bit.toString(16)).slice(-2)
    );
    return hexArr.join('');
  }

  /**
   * 检查蓝牙权限
   * @returns {Promise<boolean>} 是否已授权
   */
  private async checkBluetoothPermission() {
    try {
      const res = await Taro.getSetting();
      if (!res.authSetting['scope.bluetooth']) {
        const authRes = await Taro.authorize({ scope: 'scope.bluetooth' });
        return authRes.errMsg === 'authorize:ok';
      }
      return true;
    } catch (error) {
      console.error('检查蓝牙权限失败:', error);
      Taro.showModal({
        title: '提示',
        content: '请授权蓝牙权限以使用蓝牙功能',
        showCancel: false,
      });
      return false;
    }
  }

  /**
   * 检查定位权限
   * @returns {Promise<boolean>} 是否已授权
   */
  private async checkLocationPermission() {
    try {
      const res = await Taro.getSetting();
      if (!res.authSetting['scope.userLocation']) {
        const authRes = await Taro.authorize({ scope: 'scope.userLocation' });
        return authRes.errMsg === 'authorize:ok';
      }
      return true;
    } catch (error) {
      console.error('检查定位权限失败:', error);
      Taro.showModal({
        title: '提示',
        content: '请授权定位权限以使用蓝牙功能(安卓系统要求)',
        showCancel: false,
      });
      return false;
    }
  }

  /**
   * 处理蓝牙错误
   * @param {Object} error 错误对象
   */
  private handleBluetoothError(error: any) {
    let errorMsg = '蓝牙操作失败';
    if (error.errCode === 10001) {
      errorMsg = '蓝牙适配器不可用，请检查蓝牙是否开启';
    } else if (error.errCode === 10002) {
      errorMsg = '蓝牙未授权，请在设置中开启权限';
    } else if (error.errCode === 10003) {
      errorMsg = '定位未授权(安卓系统要求)，请在设置中开启定位权限';
    } else if (error.errCode === 10004) {
      errorMsg = '蓝牙适配器初始化失败';
    }

    Taro.showModal({
      title: '蓝牙错误',
      content: errorMsg,
      showCancel: false,
    });
  }
}

export default new BluetoothService();
